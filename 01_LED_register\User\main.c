#include <stdint.h>
int main(void)
{
    //1.时钟配置 - 开启GPIOA时钟
    *(uint32_t *)(0x40021000 + 0x10) = 0x04;  // RCC_APB2ENR，bit2开启GPIOA时钟

    //2.GPIO工作模式配置 - 配置PA0为推挽输出
    *(uint32_t *)(0x40010800 + 0x00) &= 0xFFFFFFF0;  // 清除PA0配置位(CRL寄存器)
    *(uint32_t *)(0x40010800 + 0x00) |= 0x00000003;  // PA0推挽输出，50MHz

    //3.PA0输出高电平 - 点亮黄灯
    *(uint32_t *)(0x40010800 + 0x0C) |= 0x0001;      // ODR寄存器，PA0输出高电平
    while(1)
    {

    }


}
